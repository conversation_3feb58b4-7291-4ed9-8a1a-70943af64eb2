<template>
  <div class="task-info-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item>
              <el-input
                v-model="conditions.filters.taskName"
                placeholder="请输入任务名称"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="conditions.filters.taskId"
                placeholder="请输入任务ID"
                clearable
                @keyup.enter.native="onSearch"
                @input="handleTaskIdInput"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="conditions.filters.taskStatus"
                placeholder="请选择任务状态"
                clearable
              >
                <el-option label="审核中" value="CHECKING" />
                <el-option label="进行中" value="DOING" />
                <el-option label="审核拒绝" value="REJECTED" />
                <el-option label="已完成" value="FINISHED" />
                <el-option label="已停用" value="STOP" />
              </el-select>
            </el-form-item>
            <el-form-item v-show="!needToggle">
              <el-select
                v-model="conditions.filters.businessContractId"
                placeholder="请选择服务合同"
                clearable
                filterable
              >
                <el-option
                  v-for="item in contractOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-show="!needToggle">
              <el-date-picker
                v-model="createTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="创建时间"
                end-placeholder="创建时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleCreateTimeChange"
              />
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
            <el-button v-show="needToggle" type="text" class="toggle-btn" @click="toggleMoreFilters">
              {{ showMoreFilters ? '收起' : '展开' }}
              <i :class="showMoreFilters ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </el-button>
          </div>
        </div>

        <div v-show="needToggle && showMoreFilters" class="more-form-items">
          <el-form-item>
            <el-select
              v-model="conditions.filters.businessContractId"
              placeholder="服务合同"
              clearable
              filterable
            >
              <el-option
                v-for="item in contractOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="创建时间"
              end-placeholder="创建时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 表头操作区 -->
      <div class="table-header">
        <div class="table-actions">
          <el-button type="primary" @click="handleCreateTask">
            <i class="el-icon-plus"></i>
            创建任务
          </el-button>
        </div>
      </div>

      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        row-key="taskId"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column prop="taskId" label="任务ID" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.taskId || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="taskName" label="任务名称" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.taskName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="businessContractName" label="所属服务合同" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.businessContractName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="taskTagName" label="任务标签" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.taskTagName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="taskStatus" label="任务状态" min-width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getTaskStatusTagType(scope.row.taskStatus)"
              size="medium"
              effect="light"
            >
              {{ getTaskStatusText(scope.row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskVisibility" label="任务可见性" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.taskVisibility || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="requiredCount" label="需求人数" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.requiredCount || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="applyCount" label="申请人数" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.applyCount || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="completeCount" label="完成人数" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.completeCount || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="150" :formatter="formatDateTime" />

        <el-table-column label="操作" width="320" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                v-if="scope.row.taskStatus === '审核中'"
                type="text"
                size="small"
                @click="handleAuditTask(scope.row)"
              >
                审核任务
              </el-button>
              <el-button
                v-if="scope.row.taskStatus === '审核中' || scope.row.taskStatus === '审核拒绝' || scope.row.taskStatus === '进行中'"
                type="text"
                size="small"
                @click="handleViewTask(scope.row)"
              >
                查看任务
              </el-button>
              <el-button
                v-if="scope.row.taskStatus === '进行中' || scope.row.taskStatus === '已完成'"
                type="text"
                size="small"
                @click="handleViewPersonnel(scope.row)"
              >
                查看人员
              </el-button>
              <el-button
                v-if="scope.row.taskStatus === '进行中'"
                type="text"
                size="small"
                @click="handleStopTask(scope.row)"
              >
                停用
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog
      title="创建任务"
      :visible.sync="createTaskDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="配置任务类型"></el-step>
        <el-step title="完善任务信息"></el-step>
      </el-steps>

      <!-- 步骤1：配置任务类型 -->
      <div v-show="currentStep === 0" class="step-content">
        <el-form :model="taskForm" label-width="120px" class="task-form">
          <el-form-item label="任务可见性:" required>
            <el-radio-group v-model="taskForm.visibility">
              <el-radio label="ALL">全部人员可见</el-radio>
              <el-radio label="ASSIGNED">指定人员可见</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="所属服务合同:" required>
            <el-select
              v-model="taskForm.businessContractId"
              placeholder="请选择所属服务合同"
              style="width: 100%"
              filterable
              @change="handleContractChange"
            >
              <el-option
                v-for="item in contractOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="任务标签:" required>
            <el-select
              v-model="taskForm.taskTagId"
              placeholder="请选择任务标签"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in taskTagOptions"
                :key="item.id"
                :label="item.description"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="合同模板:">
            <el-select
              v-model="taskForm.templateId"
              placeholder="请选择合同模板"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in templateOptions"
                :key="item.tempId"
                :label="item.tempName"
                :value="item.tempId"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2：完善任务信息 -->
      <div v-show="currentStep === 1" class="step-content">
        <el-form :model="taskForm" label-width="120px" class="task-form">
          <el-form-item label="任务名称:" required>
            <el-input
              v-model="taskForm.taskName"
              placeholder="请输入任务名称(不超过50字内)"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="人员要求:" required>
            <div class="personnel-requirements">
              <el-select
                v-model="taskForm.requiredGender"
                placeholder="请选择性别"
                clearable
                style="width: 32%; margin-right: 2%"
              >
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
                <el-option label="性别不限" value="性别不限" />
              </el-select>
              <el-select
                v-model="taskForm.taskAge1"
                placeholder="请选择年龄"
                clearable
                style="width: 32%; margin-right: 2%"
                @change="handleAgeRequirementChange"
              >
                <el-option label="录入年龄区间" value="录入年龄区间" />
                <el-option label="年龄不限" value="年龄不限" />
              </el-select>
              <el-select
                v-model="taskForm.requiredEducation"
                placeholder="请选择学历"
                clearable
                style="width: 32%"
              >
                <el-option label="博士研究生" value="博士研究生" />
                <el-option label="硕士研究生" value="硕士研究生" />
                <el-option label="大学本科" value="大学本科" />
                <el-option label="大学专科" value="大学专科" />
                <el-option label="学历不限" value="学历不限" />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item v-if="taskForm.taskAge1 === '录入年龄区间'" label="年龄区间:" required>
            <div class="age-range">
              <el-input
                v-model="taskForm.minAge"
                placeholder="请输入最小年龄"
                style="width: 45%"
                @input="handleAgeInput($event, 'minAge')"
                @blur="validateAgeRange"
              />
              <span style="width: 10%; text-align: center; display: inline-block">~</span>
              <el-input
                v-model="taskForm.maxAge"
                placeholder="请输入最大年龄"
                style="width: 45%"
                @input="handleAgeInput($event, 'maxAge')"
                @blur="validateAgeRange"
              />
            </div>
          </el-form-item>
          <el-form-item label="需求人数:" required>
            <el-input
              v-model="taskForm.requiredCount"
              placeholder="请输入需求人数"
              style="width: 100%"
              @input="handleNumberInput($event, 'requiredCount')"
            />
          </el-form-item>
          <el-form-item label="任务金额:" required>
            <el-select
              v-model="taskForm.amountType"
              placeholder="请选择任务金额类型"
              style="width: 100%"
              @change="handleAmountTypeChange"
            >
              <el-option label="面议" value="NEGOTIABLE" />
              <el-option label="单笔录入金额" value="SINGLE" />
              <el-option label="区间录入金额" value="RANGE" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="taskForm.amountType === 'SINGLE'" label="" required>
            <div class="single-amount">
              <el-input
                v-model="taskForm.singleAmount"
                placeholder="请输入金额"
                style="width: calc(100% - 30px); margin-right: 10px"
                @input="handleNumberInput($event, 'singleAmount')"
              />
              <span>元</span>
            </div>
          </el-form-item>
          <el-form-item v-if="taskForm.amountType === 'RANGE'" label="" required>
            <div class="amount-range">
              <el-input
                v-model="taskForm.minAmount"
                placeholder="请输入最小金额"
                style="width: 45%"
                @input="handleNumberInput($event, 'minAmount')"
              />
              <span style="width: 10%; text-align: center; display: inline-block">~</span>
              <el-input
                v-model="taskForm.maxAmount"
                placeholder="请输入最大金额"
                style="width: 35%"
                @input="handleNumberInput($event, 'maxAmount')"
              />
              <span style="margin-left: 10px">元</span>
            </div>
          </el-form-item>
          <el-form-item label="任务时间:" required>
            <el-col :span="11">
              <el-date-picker
                v-model="taskForm.taskStartTime"
                type="date"
                placeholder="开始日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="2" style="text-align: center">至</el-col>
            <el-col :span="11">
              <el-date-picker
                v-model="taskForm.taskEndTime"
                type="date"
                placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="任务地点:" required>
            <el-input
              v-model="taskForm.taskLocation"
              placeholder="请输入任务地点"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="任务描述:">
            <el-input
              v-model="taskForm.taskDescription"
              type="textarea"
              :rows="4"
              placeholder="任务描述（2000字以内）"
              show-word-limit
            />
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="submitTask" :loading="submitting">确认创建</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 查看任务详情对话框 -->
    <el-dialog
      title="查看任务详情"
      :visible.sync="viewTaskDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      @close="handleViewTaskDialogClose"
    >
      <div v-loading="viewTaskLoading" element-loading-text="加载中...">
        <el-steps :active="currentViewStep" finish-status="success" align-center>
          <el-step title="任务类型信息"></el-step>
          <el-step title="任务详细信息"></el-step>
        </el-steps>

        <!-- 步骤1：任务类型信息 -->
        <div v-show="currentViewStep === 0" class="step-content">
        <el-form :model="viewTaskForm" label-width="120px" class="task-form">
          <el-form-item label="任务可见性:">
            <el-radio-group :value="viewTaskForm.visibility" disabled>
              <el-radio label="ALL">全部人员可见</el-radio>
              <el-radio label="ASSIGNED">指定人员可见</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="所属服务合同:">
            <el-input :value="viewTaskForm.businessContractName" disabled />
          </el-form-item>
          <el-form-item label="任务标签:">
            <el-input :value="viewTaskForm.taskTagName" disabled />
          </el-form-item>
          <el-form-item label="合同模板:">
            <el-input :value="viewTaskForm.templateName" disabled />
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button  @click="nextViewStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2：任务详细信息 -->
      <div v-show="currentViewStep === 1" class="step-content">
        <el-form :model="viewTaskForm" label-width="120px" class="task-form">
          <el-form-item label="任务名称:">
            <el-input :value="viewTaskForm.taskName" disabled />
          </el-form-item>
          <el-form-item label="人员要求:">
            <div class="personnel-requirements">
              <el-input :value="viewTaskForm.requiredGender" disabled style="width: 32%; margin-right: 2%" />
              <el-input :value="formatAgeRequirement" disabled style="width: 32%; margin-right: 2%" />
              <el-input :value="viewTaskForm.requiredEducation" disabled style="width: 32%" />
            </div>
          </el-form-item>
          <el-form-item v-if="showAgeRange" label="年龄区间:">
            <div class="age-range">
              <el-input :value="ageRangeDisplay.minAge" disabled style="width: 45%" />
              <span style="width: 10%; text-align: center; display: inline-block">~</span>
              <el-input :value="ageRangeDisplay.maxAge" disabled style="width: 45%" />
            </div>
          </el-form-item>
          <el-form-item label="需求人数:">
            <el-input :value="viewTaskForm.requiredCount" disabled />
          </el-form-item>
          <el-form-item label="任务金额:">
            <el-input :value="formatAmountRequirement" disabled />
          </el-form-item>
          <el-form-item v-if="showAmountRange" label="金额区间:">
            <div class="amount-range">
              <el-input :value="amountRangeDisplay.minAmount" disabled style="width: 45%" />
              <span style="width: 10%; text-align: center; display: inline-block">~</span>
              <el-input :value="amountRangeDisplay.maxAmount" disabled style="width: 35%" />
              <span style="margin-left: 10px">元</span>
            </div>
          </el-form-item>
          <el-form-item v-if="showSingleAmount" label="单笔金额:">
            <div class="single-amount">
              <el-input :value="singleAmountDisplay" disabled style="width: calc(100% - 30px); margin-right: 10px" />
              <span>元</span>
            </div>
          </el-form-item>
          <el-form-item label="任务时间:">
            <div style="display: flex; align-items: center;">
              <el-input :value="viewTaskForm.taskStartTime" disabled style="width: 45%" />
              <span style="width: 10%; text-align: center;">至</span>
              <el-input :value="viewTaskForm.taskEndTime" disabled style="width: 45%" />
            </div>
          </el-form-item>
          <el-form-item label="任务地点:">
            <el-input :value="viewTaskForm.taskLocation" disabled />
          </el-form-item>
          <el-form-item label="任务描述:">
            <el-input
              :value="viewTaskForm.taskDescription"
              type="textarea"
              :rows="4"
              disabled
            />
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button @click="prevViewStep">上一步</el-button>
        </div>
      </div>
      </div>
    </el-dialog>

    <!-- 审核任务对话框 -->
    <el-dialog
      title="审核任务"
      :visible.sync="auditTaskDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      @close="handleAuditTaskDialogClose"
    >
      <div v-loading="auditTaskLoading" element-loading-text="加载中...">
        <el-steps :active="currentAuditStep" finish-status="success" align-center>
          <el-step title="任务类型信息"></el-step>
          <el-step title="任务详细信息"></el-step>
        </el-steps>

        <!-- 步骤1：任务类型信息 -->
        <div v-show="currentAuditStep === 0" class="step-content">
        <el-form :model="auditTaskForm" label-width="120px" class="task-form">
          <el-form-item label="任务可见性:">
            <el-radio-group :value="auditTaskForm.visibility" disabled>
              <el-radio label="ALL">全部人员可见</el-radio>
              <el-radio label="ASSIGNED">指定人员可见</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="所属服务合同:">
            <el-input :value="auditTaskForm.businessContractName" disabled />
          </el-form-item>
          <el-form-item label="任务标签:">
            <el-input :value="auditTaskForm.taskTagName" disabled />
          </el-form-item>
          <el-form-item label="合同模板:">
            <el-input :value="auditTaskForm.templateName" disabled />
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button  @click="nextAuditStep">下一步</el-button>
        </div>
      </div>

      <!-- 步骤2：任务详细信息 -->
      <div v-show="currentAuditStep === 1" class="step-content">
        <el-form :model="auditTaskForm" label-width="120px" class="task-form">
          <el-form-item label="任务名称:">
            <el-input :value="auditTaskForm.taskName" disabled />
          </el-form-item>
          <el-form-item label="人员要求:">
            <div class="personnel-requirements">
              <el-input :value="auditTaskForm.requiredGender" disabled style="width: 32%; margin-right: 2%" />
              <el-input :value="formatAuditAgeRequirement" disabled style="width: 32%; margin-right: 2%" />
              <el-input :value="auditTaskForm.requiredEducation" disabled style="width: 32%" />
            </div>
          </el-form-item>
          <el-form-item v-if="showAuditAgeRange" label="年龄区间:">
            <div class="age-range">
              <el-input :value="auditAgeRangeDisplay.minAge" disabled style="width: 45%" />
              <span style="width: 10%; text-align: center; display: inline-block">~</span>
              <el-input :value="auditAgeRangeDisplay.maxAge" disabled style="width: 45%" />
            </div>
          </el-form-item>
          <el-form-item label="需求人数:">
            <el-input :value="auditTaskForm.requiredCount" disabled />
          </el-form-item>
          <el-form-item label="任务金额:">
            <el-input :value="formatAuditAmountRequirement" disabled />
          </el-form-item>
          <el-form-item v-if="showAuditAmountRange" label="金额区间:">
            <div class="amount-range">
              <el-input :value="auditAmountRangeDisplay.minAmount" disabled style="width: 45%" />
              <span style="width: 10%; text-align: center; display: inline-block">~</span>
              <el-input :value="auditAmountRangeDisplay.maxAmount" disabled style="width: 35%" />
              <span style="margin-left: 10px">元</span>
            </div>
          </el-form-item>
          <el-form-item v-if="showAuditSingleAmount" label="单笔金额:">
            <div class="single-amount">
              <el-input :value="auditSingleAmountDisplay" disabled style="width: calc(100% - 30px); margin-right: 10px" />
              <span>元</span>
            </div>
          </el-form-item>
          <el-form-item label="任务时间:">
            <div style="display: flex; align-items: center;">
              <el-input :value="auditTaskForm.taskStartTime" disabled style="width: 45%" />
              <span style="width: 10%; text-align: center;">至</span>
              <el-input :value="auditTaskForm.taskEndTime" disabled style="width: 45%" />
            </div>
          </el-form-item>
          <el-form-item label="任务地点:">
            <el-input :value="auditTaskForm.taskLocation" disabled />
          </el-form-item>
          <el-form-item label="任务描述:">
            <el-input
              :value="auditTaskForm.taskDescription"
              type="textarea"
              :rows="4"
              disabled
            />
          </el-form-item>

          <!-- 审核结果选择 -->
          <el-form-item label="审核结果:" required>
            <el-select
              v-model="auditForm.auditResult"
              placeholder="请选择审核结果"
              style="width: 100%"
              @change="handleAuditResultChange"
            >
              <el-option label="审核通过" :value="true" />
              <el-option label="审核拒绝" :value="false" />
            </el-select>
          </el-form-item>

          <!-- 拒绝原因输入框 -->
          <el-form-item v-if="!auditForm.auditResult" label="拒绝原因:" required>
            <el-input
              v-model="auditForm.auditResultDesc"
              type="textarea"
              :rows="4"
              placeholder="请输入拒绝原因（必填）"
              show-word-limit
              maxlength="500"
            />
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button @click="prevAuditStep">上一步</el-button>
          <el-button type="primary" @click="submitAudit" :loading="auditSubmitting">确认审核</el-button>
        </div>
      </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      createTimeRange: [],
      conditions: {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          taskName: '',
          taskId: '',
          taskStatus: '',
          businessContractId: '',
          taskVisibility: '',
          taskTagId: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      showMoreFilters: false,
      tableHeight: 500,
      needToggle: false,
      resizeTimer: null,
      contractOptions: [],
      taskTagOptions: [],
      templateOptions: [],

      // 创建任务对话框相关
      createTaskDialogVisible: false,
      currentStep: 0,
      submitting: false,

      // 查看任务详情对话框相关
      viewTaskDialogVisible: false,
      currentViewStep: 0,
      viewTaskForm: {},
      viewTaskLoading: false,

      // 审核任务对话框相关
      auditTaskDialogVisible: false,
      currentAuditStep: 0,
      auditTaskForm: {},
      auditTaskLoading: false,
      auditForm: {
        auditResult: true,
        auditResultDesc: ''
      },
      auditSubmitting: false,
      taskForm: {
        visibility: 'ALL',
        businessContractId: '',
        taskTagId: '',
        templateId: '',
        taskName: '',
        requiredCount: '',
        taskStartTime: '',
        taskEndTime: '',
        taskLocation: '',
        taskDescription: '',
        requiredGender: '',
        requiredEducation: '',
        taskAge1: '',
        minAge: '',
        maxAge: '',
        amountType: '',
        singleAmount: '',
        minAmount: '',
        maxAmount: '',
        ageRequirement: {
          unlimited: true,
          minAge: 0,
          maxAge: 0
        },
        amountRequirement: {
          type: '',
          singleAmount: 0,
          minAmount: 0,
          maxAmount: 0
        }
      },
      taskStatusOptions: {
        'CHECKING': '审核中',
        'DOING': '进行中',
        'REJECTED': '审核拒绝',
        'FINISHED': '已完成',
        'STOP': '已停用'
      },
     
      currentCompleteTask: null
    }
  },
  computed: {
    // 格式化年龄要求显示
    formatAgeRequirement() {
      const requiredAge = this.viewTaskForm.requiredAge
      if (!requiredAge) {
        return '-'
      }

      if (requiredAge === '不限') {
        return '年龄不限'
      } else if (requiredAge.includes('-')) {
        return '录入年龄区间'
      } else {
        return requiredAge
      }
    },

    // 是否显示年龄区间
    showAgeRange() {
      const requiredAge = this.viewTaskForm.requiredAge
      return requiredAge && requiredAge !== '不限' && requiredAge.includes('-')
    },

    // 年龄区间显示数据
    ageRangeDisplay() {
      const requiredAge = this.viewTaskForm.requiredAge
      if (requiredAge && requiredAge.includes('-')) {
        const [minAge, maxAge] = requiredAge.split('-')
        return {
          minAge: minAge.trim(),
          maxAge: maxAge.trim()
        }
      }
      return { minAge: '', maxAge: '' }
    },

    // 格式化金额要求显示
    formatAmountRequirement() {
      const taskAmount = this.viewTaskForm.taskAmount
      if (!taskAmount) {
        return '-'
      }

      if (taskAmount === '面议') {
        return '面议'
      } else if (taskAmount.includes(' - ')) {
        return '区间录入金额'
      } else {
        return '单笔录入金额'
      }
    },

    // 是否显示金额区间
    showAmountRange() {
      const taskAmount = this.viewTaskForm.taskAmount
      return taskAmount && taskAmount.includes(' - ')
    },

    // 金额区间显示数据
    amountRangeDisplay() {
      const taskAmount = this.viewTaskForm.taskAmount
      if (taskAmount && taskAmount.includes(' - ')) {
        const [minAmount, maxAmount] = taskAmount.split(' - ')
        return {
          minAmount: minAmount.trim(),
          maxAmount: maxAmount.trim()
        }
      }
      return { minAmount: '', maxAmount: '' }
    },

    // 是否显示单笔金额
    showSingleAmount() {
      const taskAmount = this.viewTaskForm.taskAmount
      return taskAmount && taskAmount !== '面议' && !taskAmount.includes(' - ')
    },

    // 单笔金额显示
    singleAmountDisplay() {
      const taskAmount = this.viewTaskForm.taskAmount
      if (taskAmount && taskAmount !== '面议' && !taskAmount.includes(' - ')) {
        return taskAmount
      }
      return ''
    },

    // 审核任务对话框相关计算属性
    // 格式化年龄要求显示 - 审核
    formatAuditAgeRequirement() {
      const requiredAge = this.auditTaskForm.requiredAge
      if (!requiredAge) {
        return '-'
      }

      if (requiredAge === '不限') {
        return '年龄不限'
      } else if (requiredAge.includes('-')) {
        return '录入年龄区间'
      } else {
        return requiredAge
      }
    },

    // 是否显示年龄区间 - 审核
    showAuditAgeRange() {
      const requiredAge = this.auditTaskForm.requiredAge
      return requiredAge && requiredAge !== '不限' && requiredAge.includes('-')
    },

    // 年龄区间显示数据 - 审核
    auditAgeRangeDisplay() {
      const requiredAge = this.auditTaskForm.requiredAge
      if (requiredAge && requiredAge.includes('-')) {
        const [minAge, maxAge] = requiredAge.split('-')
        return {
          minAge: minAge.trim(),
          maxAge: maxAge.trim()
        }
      }
      return { minAge: '', maxAge: '' }
    },

    // 格式化金额要求显示 - 审核
    formatAuditAmountRequirement() {
      const taskAmount = this.auditTaskForm.taskAmount
      if (!taskAmount) {
        return '-'
      }

      if (taskAmount === '面议') {
        return '面议'
      } else if (taskAmount.includes(' - ')) {
        return '区间录入金额'
      } else {
        return '单笔录入金额'
      }
    },

    // 是否显示金额区间 - 审核
    showAuditAmountRange() {
      const taskAmount = this.auditTaskForm.taskAmount
      return taskAmount && taskAmount.includes(' - ')
    },

    // 金额区间显示数据 - 审核
    auditAmountRangeDisplay() {
      const taskAmount = this.auditTaskForm.taskAmount
      if (taskAmount && taskAmount.includes(' - ')) {
        const [minAmount, maxAmount] = taskAmount.split(' - ')
        return {
          minAmount: minAmount.trim(),
          maxAmount: maxAmount.trim()
        }
      }
      return { minAmount: '', maxAmount: '' }
    },

    // 是否显示单笔金额 - 审核
    showAuditSingleAmount() {
      const taskAmount = this.auditTaskForm.taskAmount
      return taskAmount && taskAmount !== '面议' && !taskAmount.includes(' - ')
    },

    // 单笔金额显示 - 审核
    auditSingleAmountDisplay() {
      const taskAmount = this.auditTaskForm.taskAmount
      if (taskAmount && taskAmount !== '面议' && !taskAmount.includes(' - ')) {
        return taskAmount
      }
      return ''
    }
  },
  async created() {
    await this.loadOptions()
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.handleResize)
  },
  mounted() {
    this.$nextTick(() => {
      this.checkNeedToggle()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
  },
  methods: {
    async loadOptions() {
      // 加载合同选项 - 使用supplier接口
      // const [contractErr, contractRes] = await client.supplierGetAllContracts({ body: {} })
       const [contractErr, contractRes] = await client.supplierListContract({
          body: { filters: {} }
        })
      if (!contractErr) {
        this.contractOptions = contractRes.data.list || []
      }

      // 加载任务标签选项数据 - 使用supplier接口
      const [tagErr, tagRes] = await client.supplierQueryTaskTag()
      if (!tagErr) {
        this.taskTagOptions = tagRes.data || []
      } else {
        this.taskTagOptions = []
      }
    },

    async getList() {
      this.loading = true

      // 使用supplier接口查询任务列表
      const [err, r] = await client.supplierQueryTask({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          taskName: '',
          taskId: '',
          taskStatus: '',
          businessContractId: '',
          taskVisibility: '',
          taskTagId: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      }
      this.createTimeRange = []
      // 重置后自动查询
      this.$nextTick(() => {
        this.getList()
      })
    },

    formatDateTime(_, __, cellValue) {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    getTaskStatusText(status) {
      // 由于后端直接返回中文，直接显示即可
      return status || '-'
    },

    getTaskStatusTagType(status) {
      const typeMap = {
        '审核中': 'warning',
        '进行中': 'success',
        '审核拒绝': 'danger',
        '已完成': 'success',
        '已停用': 'info'
      }
      return typeMap[status] || 'info'
    },

    // 查看任务详情
    async handleViewTask(row) {
      this.viewTaskDialogVisible = true
      this.currentViewStep = 0
      this.viewTaskLoading = true

      try {
        // 使用supplier接口获取任务详情
        const [err, res] = await client.supplierGetTaskDetail(row.taskId)
        if (err) {
          handleError(err)
          this.viewTaskDialogVisible = false
          return
        }

        const taskData = res.data
        this.viewTaskForm = {
          ...taskData,
          businessContractName: row.businessContractName,
          taskTagName: row.taskTagName || '-',
        }
      } finally {
        this.viewTaskLoading = false
      }
    },

    // 查看人员
    handleViewPersonnel(row) {
      this.$router.push({
        path: `/taskInfo/${row.taskId}/personnel`
      })
    },

    // 审核任务
    async handleAuditTask(row) {
      this.auditTaskDialogVisible = true
      this.currentAuditStep = 0
      this.auditTaskLoading = true
      this.resetAuditForm()

      try {
        // 使用supplier接口获取任务详情
        const [err, res] = await client.supplierGetTaskDetail(row.taskId)
        if (err) {
          handleError(err)
          this.auditTaskDialogVisible = false
          return
        }

        const taskData = res.data
        this.auditTaskForm = {
          ...taskData,
          businessContractName: row.businessContractName,
          taskTagName: row.taskTagName || '-',
          taskId: row.taskId
        }
      } finally {
        this.auditTaskLoading = false
      }
    },

    // 停用任务
    async handleStopTask(row) {
      try {
        await this.$confirm(`确定要停用任务 "${row.taskName}" 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用停用任务接口 - 使用supplier接口
        const [err] = await client.supplierDisableTask({
          body: {
            taskId: row.taskId
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('任务停用成功')
        // 刷新列表
        this.getList()
      } catch (error) {
        // User cancelled, do nothing
      }
    },


    // 任务完成回调
    onTaskCompleted() {
      this.getList()
    },

    // 处理合同变更
    async handleContractChange(contractId) {
      if (!contractId) {
        this.templateOptions = []
        return
      }

      // 使用supplier接口获取合同模板
      const [err, res] = await client.listCorporationTemp({
        body: { businessContractId: contractId }
      })

      if (!err) {
        this.templateOptions = res.data || []
      } else {
        this.templateOptions = []
        handleError(err)
      }

      // 清空已选择的模板
      this.taskForm.templateId = ''
    },

    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },

    handleResize() {
      // 防抖处理，避免频繁触发
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        this.setTableHeight()
        this.checkNeedToggle()
      }, 150)
    },

    checkNeedToggle() {
      this.$nextTick(() => {
        try {
          const searchMain = this.$el?.querySelector('.search-main')
          const searchActions = this.$el?.querySelector('.search-actions')
          if (!searchMain || !searchActions) return

          const mainFormItems = searchMain.querySelectorAll('.el-form-item')
          let totalWidth = 0

          mainFormItems.forEach(item => {
            if (item.style.display !== 'none' && getComputedStyle(item).display !== 'none') {
              // 确保元素已渲染完成
              const itemWidth = item.offsetWidth || 200 // 如果获取不到宽度，使用默认值
              totalWidth += itemWidth + 24
            }
          })

          const searchContainer = this.$el?.querySelector('.search-container')
          const actionsWidth = searchActions.offsetWidth + 20 // 动态获取操作按钮宽度
          const containerWidth = searchContainer ? searchContainer.offsetWidth : window.innerWidth
          const availableWidth = containerWidth - actionsWidth - 40 // 额外预留40px边距

          this.needToggle = totalWidth > availableWidth

          if (!this.needToggle) {
            this.showMoreFilters = false
          }
        } catch (error) {
          console.warn('检测展开/收起状态时出错:', error);
          // 发生错误时，根据屏幕宽度简单判断
          this.needToggle = window.innerWidth < 1200
        }
      })
    },

    toggleMoreFilters() {
      this.showMoreFilters = !this.showMoreFilters
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    setTableHeight() {
      try {
        const windowHeight = window.innerHeight;
        const searchContainer = this.$el?.querySelector('.search-container');
        const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
        const tableHeader = this.$el?.querySelector('.table-header');
        const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
        const pagination = this.$el?.querySelector('.pagination-container');
        const paginationHeight = pagination ? pagination.offsetHeight : 40;
        const padding = 40;

        const availableHeight = windowHeight - searchHeight - tableHeaderHeight - paginationHeight - padding;

        if (this.data.length <= 5) {
          this.tableHeight = 500;
        } else {
          const minHeight = 300;
          const maxHeight = windowHeight - searchHeight - tableHeaderHeight - paginationHeight - padding - 5;
          this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
        }

        if (this.tableHeight) {
          this.tableHeight = Math.floor(this.tableHeight);
        }
      } catch (error) {
        console.warn('设置表格高度时出错:', error);
        this.tableHeight = 500; // 设置默认高度
      }
    },

    // 创建任务相关方法
    handleCreateTask() {
      this.createTaskDialogVisible = true
      this.currentStep = 0
      this.resetTaskForm()
    },

    handleDialogClose() {
      this.createTaskDialogVisible = false
      this.currentStep = 0
      this.resetTaskForm()
    },

    // 查看任务详情对话框相关方法
    handleViewTaskDialogClose() {
      this.viewTaskDialogVisible = false
      this.currentViewStep = 0
      this.viewTaskForm = {}
      this.viewTaskLoading = false
    },

    nextViewStep() {
      this.currentViewStep = 1
    },

    prevViewStep() {
      this.currentViewStep = 0
    },

    // 审核任务对话框相关方法
    handleAuditTaskDialogClose() {
      this.auditTaskDialogVisible = false
      this.currentAuditStep = 0
      this.auditTaskForm = {}
      this.auditTaskLoading = false
      this.resetAuditForm()
    },

    nextAuditStep() {
      this.currentAuditStep = 1
    },

    prevAuditStep() {
      this.currentAuditStep = 0
    },

    resetAuditForm() {
      this.auditForm = {
        auditResult: true,
        auditResultDesc: ''
      }
    },

    // 审核结果变化处理
    handleAuditResultChange(value) {
      if (value) {
        // 审核通过时清空拒绝原因
        this.auditForm.auditResultDesc = ''
      }
    },

    // 提交审核
    async submitAudit() {
      // 验证审核拒绝时必须填写拒绝原因
      if (!this.auditForm.auditResult && !this.auditForm.auditResultDesc.trim()) {
        this.$message.warning('审核拒绝时必须填写拒绝原因')
        return
      }

      this.auditSubmitting = true

      try {
        // 调用审核任务接口
        const [err] = await client.supplierAuditTask({
          body: {
            taskId: this.auditTaskForm.taskId,
            auditResult: this.auditForm.auditResult,
            auditResultDesc: this.auditForm.auditResultDesc.trim()
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('审核提交成功')
        this.auditTaskDialogVisible = false
        // 刷新列表
        this.getList()
      } finally {
        this.auditSubmitting = false
      }
    },

    resetTaskForm() {
      this.taskForm = {
        visibility: 'ALL',
        businessContractId: '',
        taskTagId: '',
        templateId: '',
        taskName: '',
        requiredCount: '',
        taskStartTime: '',
        taskEndTime: '',
        taskLocation: '',
        taskDescription: '',
        requiredGender: '',
        requiredEducation: '',
        taskAge1: '',
        minAge: '',
        maxAge: '',
        amountType: '',
        singleAmount: '',
        minAmount: '',
        maxAmount: '',
        ageRequirement: {
          unlimited: true,
          minAge: 0,
          maxAge: 0
        },
        amountRequirement: {
          type: '',
          singleAmount: 0,
          minAmount: 0,
          maxAmount: 0
        }
      }
    },

    nextStep() {
      // 验证第一步的必填项
      if (!this.taskForm.visibility || !this.taskForm.businessContractId || !this.taskForm.taskTagId) {
        this.$message.warning('请填写完整的任务类型信息')
        return
      }
      this.currentStep = 1
    },

    prevStep() {
      this.currentStep = 0
    },

    handleAgeRequirementChange(value) {
      if (value !== '录入年龄区间') {
        this.taskForm.minAge = ''
        this.taskForm.maxAge = ''
      }
    },

    handleAgeInput(value, field) {
      // 只允许输入数字
      const numericValue = value.replace(/[^\d]/g, '')
      this.taskForm[field] = numericValue
    },

    validateAgeRange() {
      if (this.taskForm.minAge && this.taskForm.maxAge) {
        const minAge = parseInt(this.taskForm.minAge)
        const maxAge = parseInt(this.taskForm.maxAge)

        if (minAge < 1) {
          this.taskForm.minAge = '1'
          this.$message.warning('最小年龄不能小于1岁')
        } else if (minAge > 100) {
          this.taskForm.minAge = '100'
          this.$message.warning('最小年龄不能大于100岁')
        }

        if (maxAge < 1) {
          this.taskForm.maxAge = '1'
          this.$message.warning('最大年龄不能小于1岁')
        } else if (maxAge > 100) {
          this.taskForm.maxAge = '100'
          this.$message.warning('最大年龄不能大于100岁')
        }

        if (parseInt(this.taskForm.minAge) > parseInt(this.taskForm.maxAge)) {
          this.$message.warning('最小年龄不能大于最大年龄')
        }
      }
    },

    handleNumberInput(value, field) {
      // 只允许输入数字
      const numericValue = value.replace(/[^\d]/g, '')
      this.taskForm[field] = numericValue
    },

    handleTaskIdInput(value) {
      // 任务ID只允许输入数字
      const numericValue = value.replace(/[^\d]/g, '')
      this.conditions.filters.taskId = numericValue
    },

    handleAmountTypeChange() {
      // 清空金额相关字段
      this.taskForm.singleAmount = ''
      this.taskForm.minAmount = ''
      this.taskForm.maxAmount = ''
    },

    async submitTask() {
      // 验证第二步的必填项
      if (!this.taskForm.taskName || !this.taskForm.requiredCount ||
          !this.taskForm.taskStartTime || !this.taskForm.taskEndTime ||
          !this.taskForm.taskLocation || !this.taskForm.requiredGender ||
          !this.taskForm.requiredEducation || !this.taskForm.taskAge1 ||
          !this.taskForm.amountType) {
        this.$message.warning('请填写完整的任务信息')
        return
      }

      // 验证年龄区间
      if (this.taskForm.taskAge1 === '录入年龄区间') {
        if (!this.taskForm.minAge || !this.taskForm.maxAge) {
          this.$message.warning('请填写完整的年龄区间')
          return
        }
        if (parseInt(this.taskForm.minAge) > parseInt(this.taskForm.maxAge)) {
          this.$message.warning('最小年龄不能大于最大年龄')
          return
        }
      }

      // 验证金额信息
      if (this.taskForm.amountType === 'SINGLE' && !this.taskForm.singleAmount) {
        this.$message.warning('请输入单笔金额')
        return
      }
      if (this.taskForm.amountType === 'RANGE') {
        if (!this.taskForm.minAmount || !this.taskForm.maxAmount) {
          this.$message.warning('请填写完整的金额区间')
          return
        }
        if (parseInt(this.taskForm.minAmount) > parseInt(this.taskForm.maxAmount)) {
          this.$message.warning('最小金额不能大于最大金额')
          return
        }
      }

      this.submitting = true

      // 构建年龄要求数据
      let ageRequirement = this.taskForm.ageRequirement
      if (this.taskForm.taskAge1 === '录入年龄区间') {
        ageRequirement = {
          unlimited: false,
          minAge: parseInt(this.taskForm.minAge) || 0,
          maxAge: parseInt(this.taskForm.maxAge) || 0
        }
      } else if (this.taskForm.taskAge1 === '年龄不限') {
        ageRequirement = {
          unlimited: true,
          minAge: 0,
          maxAge: 0
        }
      }

      // 构建金额要求数据
      let amountRequirement = {
        type: this.taskForm.amountType,
        singleAmount: 0,
        minAmount: 0,
        maxAmount: 0
      }

      if (this.taskForm.amountType === 'SINGLE') {
        amountRequirement.singleAmount = parseInt(this.taskForm.singleAmount) || 0
      } else if (this.taskForm.amountType === 'RANGE') {
        amountRequirement.minAmount = parseInt(this.taskForm.minAmount) || 0
        amountRequirement.maxAmount = parseInt(this.taskForm.maxAmount) || 0
      }

      // 使用supplier接口创建任务
      const [err] = await client.supplierAddTask({
        body: {
          visibility: this.taskForm.visibility,
          businessContractId: this.taskForm.businessContractId,
          taskTagId: this.taskForm.taskTagId,
          templateId: this.taskForm.templateId || null,
          taskName: this.taskForm.taskName,
          requiredGender: this.taskForm.requiredGender,
          requiredEducation: this.taskForm.requiredEducation,
          requiredCount: parseInt(this.taskForm.requiredCount) || 0,
          taskStartTime: this.taskForm.taskStartTime,
          taskEndTime: this.taskForm.taskEndTime,
          taskLocation: this.taskForm.taskLocation,
          taskDescription: this.taskForm.taskDescription,
          ageRequirement: ageRequirement,
          amountRequirement: amountRequirement
        }
      })

      this.submitting = false

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('任务创建成功')
      this.createTaskDialogVisible = false
      this.getList()
    }
  }
}
</script>

<style scoped>
.task-info-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
  min-width: 60px;
  text-align: right;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.search-actions .toggle-btn {
  padding: 8px 12px;
  font-weight: 500;
  transition: all 0.3s;
}

.search-actions .toggle-btn:hover {
  background-color: #ecf5ff;
}

.more-form-items {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.more-form-items .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.more-form-items .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
  min-width: 60px;
  text-align: right;
}

.more-form-items .el-form-item .el-form-item__content {
  width: 300px;
}

/* 动画效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.el-form-item .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-form-item .el-input,
.el-form-item .el-select,
.el-form-item .el-date-editor {
  width: 100%;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions .el-button {
  border-radius: 4px;
  padding: 9px 20px;
  transition: all 0.3s;
}

.table-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.table-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

/* 修复表格滚动条位置 - 彻底隐藏表格内部滚动条 */
.el-table .el-table__body-wrapper {
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

.el-table .el-table__header-wrapper {
  overflow-x: hidden !important;
}

.el-table__body-wrapper::-webkit-scrollbar,
.el-table__header-wrapper::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 为表格容器添加滚动条 */
.table-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
  margin-bottom: 10px;
  min-height: 200px; /* 确保即使没有数据也有足够高度显示滚动条 */
}

.table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 强制表格使用容器的滚动条 */
.el-table {
  width: max-content;
  min-width: 100%;
}

/* 处理空数据状态的滚动条 */
.el-table__empty-block {
  width: 100% !important;
  min-width: 1200px; /* 确保空数据时也有足够宽度触发滚动条 */
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
  width: 100%;
}

/* 状态标签样式 */
.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.status-tag.draft {
  background: #f4f4f5;
  color: #909399;
}

.status-tag.running {
  background: #e1f3d8;
  color: #67c23a;
}

.status-tag.paused {
  background: #fdf6ec;
  color: #e6a23c;
}

.status-tag.completed {
  background: #f0f9ff;
  color: #409eff;
}

.status-tag.disabled {
  background: #fef0f0;
  color: #f56c6c;
}

/* 可见性标签样式 */
.visibility-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 80px;
}

.visibility-tag.public {
  background: #e1f3d8;
  color: #67c23a;
}

.visibility-tag.private {
  background: #fdf6ec;
  color: #e6a23c;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 创建任务对话框样式 */
.step-content {
  margin-top: 30px;
  min-height: 400px;
}

.task-form {
  padding: 0 20px;
}

/* 人员要求样式 */
.personnel-requirements {
  display: flex;
  align-items: center;
  width: 100%;
}

.age-range {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 金额相关样式 */
.single-amount {
  display: flex;
  align-items: center;
  width: 100%;
}

.amount-range {
  display: flex;
  align-items: center;
  width: 100%;
}

.step-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
}

.step-actions .el-button {
  margin: 0 10px;
  padding: 10px 30px;
}

/* 对话框内容样式 */
.dialog-content {
  padding: 20px 0;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.form-section {
  margin-bottom: 24px;
}

.form-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 16px;
  padding-left: 8px;
  border-left: 3px solid #409eff;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 响应式调整 - 智能展开/收起功能（与其他页面保持一致） */
@media screen and (min-width: 1200px) {
  .search-header {
    padding-right: 200px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 200px;
  }

  .search-main .el-form-item:last-child .el-form-item__content {
    width: 180px;
  }
}

@media screen and (max-width: 1199px) {
  .search-header {
    padding-right: 180px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 180px;
  }
}

@media screen and (max-width: 992px) {
  .search-header {
    padding-right: 160px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 160px;
  }
}

@media screen and (max-width: 768px) {
  .search-header {
    padding-right: 140px;
  }

  .search-main {
    gap: 8px 16px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 140px;
  }

  .more-form-items .el-form-item .el-form-item__content {
    width: 210px;
  }
}

/* 超小屏幕：查询按钮移到下方，保持单行布局 */
@media screen and (max-width: 480px) {
  .search-header {
    padding-right: 0;
    min-height: auto;
  }

  .search-actions {
    position: static;
    margin-top: 16px;
    justify-content: flex-start;
    height: auto;
  }

  .search-main {
    gap: 4px 8px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 80px;
  }

  .more-form-items .el-form-item .el-form-item__content {
    width: 120px;
  }
}
</style>
